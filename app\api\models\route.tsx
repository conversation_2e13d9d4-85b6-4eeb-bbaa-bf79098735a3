import { auth } from "@/auth"
import { prisma } from "@/lib/db"

export const GET = auth(async (req) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const { searchParams } = new URL(req.url)
    const search = searchParams.get("search") || ""
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "50")

    const where: any = {}
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ]
    }

    const total = await prisma.model.count({ where })
    const models = await prisma.model.findMany({
      where,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { name: "asc" },
    })

    return Response.json({ models, pagination: { total, page, limit, totalPages: Math.ceil(total / limit) } })
  } catch (error) {
    console.error("Error fetching models:", error)
    return new Response("Internal server error", { status: 500 })
  }
})

export const POST = auth(async (req) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const body = await req.json()
    const {
      sku,
      name,
      description,
      technicalSpecifications,
      technicalSpecificationsLink,
      unitOfMeasure,
      barcode,
      costPerUnit,
      estimatedLeadTimeDays,
    } = body

    if (!sku || !name || !unitOfMeasure || costPerUnit === undefined || estimatedLeadTimeDays === undefined) {
      return new Response("Missing required fields", { status: 400 })
    }

    // Create model
    const created = await prisma.model.create({
      data: {
        sku,
        name,
        description,
        technicalSpecifications,
        technicalSpecificationsLink,
        unitOfMeasure,
        barcode,
        costPerUnit: Number(costPerUnit),
        estimatedLeadTimeDays: Number(estimatedLeadTimeDays),
      },
    })

    return Response.json(created, { status: 201 })
  } catch (error) {
    console.error("Error creating model:", error)
    return new Response("Internal server error", { status: 500 })
  }
})