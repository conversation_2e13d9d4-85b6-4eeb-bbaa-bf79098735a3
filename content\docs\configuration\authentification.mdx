---
title: Authentification
description: How to config the authentification.
---

<Callout type="note" twClass="mt-0">
  The auth part is similar at the [authjs](https://authjs.dev/) documentation.
  You can follow the official documentation
  [here](https://authjs.dev/getting-started/installation#setup-environment) if
  you want.
</Callout>

<Callout type="danger" twClass="mt-3">
  After upgrade Auth.js to v5: `NEXTAUTH_URL` has removed from `.env` file. Same
  thing **in production**.
</Callout>

## Update auth_secret variable

The only environment variable that is mandatory is the `AUTH_SECRET`. This is a random value used by the library to encrypt tokens and email verification hashes. You can generate one via running:

```bash
npx auth secret
```

Alternatively, you can use the `openssl` CLI, `openssl rand -base64 33`.

Then add it to your `.env` file:

```js title=".env.local"
AUTH_SECRET = secret;
```

## Update google client variables

In this section, you can update these variables:

```js title=".env.local"
GOOGLE_CLIENT_ID = your_secret_client_id.apps.googleusercontent.com;
GOOGLE_CLIENT_SECRET = your_secret_client;
```

### Video explanation

Watch a youtube video from [CodeWithAntonio](https://www.youtube.com/@codewithantonio) on how to configure the environment variables for NextAuth with Google authentication.

<iframe
  className="mt-6 aspect-video w-full"
  src="https://www.youtube.com/embed/1MTyCvS05V4"
  title="Next Auth V5 - Advanced Guide (2024)"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
  referrerPolicy="strict-origin-when-cross-origin"
  allowFullScreen
></iframe>

- GoogleAuth **local** config at [3:24:30](https://www.youtube.com/watch?v=1MTyCvS05V4&t=12270s)
- Update GoogleAuth after **deployment** at [7:52:31](https://www.youtube.com/watch?v=1MTyCvS05V4&t=12270s)

### Google config for this repo

<img
  className="mt-4 overflow-hidden rounded-lg border"
  src="/_static/docs/gg-auth-config.jpg"
/>
