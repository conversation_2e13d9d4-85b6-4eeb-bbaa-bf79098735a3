---
title: Config Files
description: Make it your own with config files.
---

The `config` folder houses various files ready for customization to suit your needs.

## List of files

Here's the list of config files available:

<Steps>

### Navigations

`marketing.ts`, `dashboard.ts` and `docs.ts` contain configuration links tailored for their respective sections.

### Site

In `site.ts`, manage metadata and footer links effortlessly.

</Steps>
