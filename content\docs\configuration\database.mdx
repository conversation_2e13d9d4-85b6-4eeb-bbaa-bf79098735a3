---
title: Database
description: How to config your Neon database.
---

The project was start with PlanetScale like database. But the free tier has been recently removed.

Currently this project use **Neon** on the free plan. Check their [pricing page](https://neon.tech/pricing) for more informations.

## Steps

<Steps>

### Create Neon account

If don't have an account on Neon, just follow their steps [here](https://neon.tech/docs/get-started-with-neon/signing-up) for create one.

### Copy database url

After create a project on Neon, you can copy the database url. Select `Prisma` from the list then the `env` tab.

<Callout type="warning">
  <b>Don't forget</b> to reveal the information hidden behind the stars using
  the eye icon. It's not very visible in dark mode.
</Callout>

Paste in your `.env` file.

```js
DATABASE_URL = "postgres://alex:<EMAIL>/dbname";
```

### Push migrations

You can push the migrations on your database with the cli.

```bash
npx prisma db push
```

</Steps>
