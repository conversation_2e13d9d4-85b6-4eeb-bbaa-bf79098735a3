
import { auth } from "@/auth"
import { prisma } from "@/lib/db"

export const GET = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
    })

    if (!user) {
      return new Response("User not found", { status: 404 })
    }

    return Response.json(user)
  } catch (error) {
    console.error("Error fetching user:", error)
    return new Response("Internal server error", { status: 500 })
  }
})