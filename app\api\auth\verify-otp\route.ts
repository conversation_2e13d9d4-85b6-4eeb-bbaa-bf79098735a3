import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function POST(req: Request) {
  try {
    const { email, otp } = await req.json();
    if (!email || !otp) {
      return NextResponse.json({ message: "Email and OTP are required." }, { status: 400 });
    }
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user || !user.otp || !user.otpExpires) {
      return NextResponse.json({ message: "OTP not found." }, { status: 400 });
    }
    if (user.otp !== otp) {
      return NextResponse.json({ message: "Invalid OTP." }, { status: 400 });
    }
    if (user.otpExpires < new Date()) {
      return NextResponse.json({ message: "OTP expired." }, { status: 400 });
    }
    await prisma.user.update({
      where: { email },
      data: {
        otp: null,
        otpExpires: null,
        emailVerified: new Date(),
      },
    });
    return NextResponse.json({ message: "OTP verified successfully." }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: "Internal server error." }, { status: 500 });
  }
}
