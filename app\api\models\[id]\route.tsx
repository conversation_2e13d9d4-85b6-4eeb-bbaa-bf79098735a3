import { auth } from "@/auth"
import { prisma } from "@/lib/db"

export const GET = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const model = await prisma.model.findUnique({ where: { id: params.id } })
    if (!model) return new Response("Model not found", { status: 404 })
    return Response.json(model)
  } catch (error) {
    console.error("Error fetching model:", error)
    return new Response("Internal server error", { status: 500 })
  }
})

export const PUT = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const body = await req.json()
    const updateData: any = {}
    const fields = [
      "sku",
      "name",
      "description",
      "technicalSpecifications",
      "technicalSpecificationsLink",
      "unitOfMeasure",
      "barcode",
      "costPerUnit",
      "estimatedLeadTimeDays",
    ]

    for (const f of fields) {
      if (body[f] !== undefined) updateData[f] = body[f]
    }

    if (updateData.costPerUnit !== undefined) updateData.costPerUnit = Number(updateData.costPerUnit)
    if (updateData.estimatedLeadTimeDays !== undefined) updateData.estimatedLeadTimeDays = Number(updateData.estimatedLeadTimeDays)

    const existing = await prisma.model.findUnique({ where: { id: params.id } })
    if (!existing) return new Response("Model not found", { status: 404 })

    const updated = await prisma.model.update({ where: { id: params.id }, data: updateData })
    return Response.json(updated)
  } catch (error) {
    console.error("Error updating model:", error)
    return new Response("Internal server error", { status: 500 })
  }
})

export const DELETE = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const existing = await prisma.model.findUnique({ where: { id: params.id } })
    if (!existing) return new Response("Model not found", { status: 404 })

    await prisma.model.delete({ where: { id: params.id } })
    return Response.json({ message: "Model deleted" })
  } catch (error) {
    console.error("Error deleting model:", error)
    return new Response("Internal server error", { status: 500 })
  }
})