import { prisma } from "@/lib/db";

export const getUserByEmail = async (email: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
      select: {
        name: true,
        emailVerified: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const getUserById = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({ where: { id } });

    return user;
  } catch {
    return null;
  }
};

export const getListOfUsers = async (page : number, perPage : number, search : string, filters : any, sort : any) => {
  try {
    const users = await prisma.user.findMany({
      skip: page * perPage,
      take: perPage,
      where: {
        ...filters,
        ...(search ? { OR: [{ name: { contains: search } }, { email: { contains: search } }] } : {}),
      },
      orderBy: sort,
    });
    return users;
  } catch {
    return null;
  }
};