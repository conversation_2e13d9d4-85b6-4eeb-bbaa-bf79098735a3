---
title: Installation
description: How to install the project.
---

<Steps>

### Create project

Start by creating a new Next.js project using `create-next-app`:

```bash
npx create-next-app my-next-project --example "https://github.com/mickasmt/next-auth-roles-template"
```

Or deploy with Vercel :

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fmickasmt%2Fnext-auth-roles-template)

<Callout type="warning" twClass="mt-4">
  A good way to create your repository, but the deployment will fail because you
  need to add your environment variables locally in your project. Follow the
  documentation for that.
</Callout>

### Install dependencies

Enter in the folder and install dependencies for your project:

```bash
cd my-next-project
pnpm install
```

### Create a `.env` file

Copy/paste the `.env.example` in the `.env` file:

```md title=".env"
NEXT_PUBLIC_APP_URL=http://localhost:3000

AUTH_SECRET=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

DATABASE_URL=

RESEND_API_KEY=
```

### Remove parts you don't want

You can use the command `pnpm run remove-content` in your terminal to remove specific parts of your project. This command supports the following parameters:

- **--blog**: Removes the blog section from your project.
- **--docs**: Removes the documentation section from your project.

#### Recommendations

- **Remove Both Sections**: It is recommended to use `pnpm run remove-content` without specifying parameters (`--blog` or `--docs`). This ensures that all associated documents are properly removed, as some documents may not be deleted/updated if you remove "blog" after "docs" or vice versa.

#### Examples

1. To remove both the blog and documentation sections:

```bash
pnpm run remove-content
```

2. To remove only the blog section:

```bash
pnpm run remove-content --blog
```

3. To remove only the documentation section:

```bash
pnpm run remove-content --docs
```

### Configuration part

Let's check the configuration part for update all environment variables before use `pnpm run dev`.

</Steps>
