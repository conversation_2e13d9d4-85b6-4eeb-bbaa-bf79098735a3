"use client";

import * as React from "react";
import { Input } from "@/components/ui/input";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { signIn } from "next-auth/react";

interface OTPVerifyFormProps {
  email: string;
  password?: string;
  autoSignIn?: boolean;
  onSuccess?: () => void;
}

export function OTPVerifyForm({ email, password, autoSignIn, onSuccess }: OTPVerifyFormProps) {
  const [otp, setOtp] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [timer, setTimer] = React.useState(60); // 60 seconds
  const [isResending, setIsResending] = React.useState(false);
  const router = typeof window !== "undefined" ? require("next/navigation").useRouter() : undefined;

  React.useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => setTimer(t => t - 1), 1000);
      return () => clearInterval(interval);
    }
  }, [timer]);

  async function handleVerify(e: React.FormEvent) {
    e.preventDefault();
    setIsLoading(true);
    const res = await fetch("/api/auth/verify-otp", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, otp })
    });
    setIsLoading(false);
    if (!res.ok) {
      const result = await res.json().catch(() => ({}));
      return toast.error(result?.message || "OTP verification failed");
    }
    toast.success("OTP verified! Signing you in...");
    if (autoSignIn && password) {
      const signInResult = await signIn("credentials", {
        email,
        password,
        redirect: false,
        callbackUrl: "/dashboard",
      });
      if (signInResult?.ok) {
        router?.push("/dashboard");
      } else {
        toast.error("Sign in failed after verification. Please try logging in.");
      }
    }
    if (onSuccess) onSuccess();
  }

  async function handleResend() {
    setIsResending(true);
    const res = await fetch("/api/auth/resend-otp", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email })
    });
    setIsResending(false);
    if (!res.ok) {
      const result = await res.json().catch(() => ({}));
      return toast.error(result?.message || "Failed to resend OTP");
    }
    setTimer(60);
    toast.success("OTP resent! Check your email.");
  }

  return (
    <form onSubmit={handleVerify} className="grid gap-4">
      <Input
        id="otp"
        placeholder="Enter OTP"
        type="text"
        value={otp}
        onChange={e => setOtp(e.target.value)}
        maxLength={6}
        autoComplete="one-time-code"
        required
      />
      <button className={cn(buttonVariants())} disabled={isLoading}>
        {isLoading ? "Verifying..." : "Verify OTP"}
      </button>
      <div className="flex items-center gap-2">
        <button
          type="button"
          className={cn(buttonVariants({ variant: "outline" }))}
          onClick={handleResend}
          disabled={timer > 0 || isResending}
        >
          {isResending ? "Resending..." : "Resend OTP"}
        </button>
        {timer > 0 && (
          <span className="text-xs text-muted-foreground">Resend available in {timer}s</span>
        )}
      </div>
    </form>
  );
}
