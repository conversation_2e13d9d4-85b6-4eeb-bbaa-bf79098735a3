-- CreateTable
CREATE TABLE "models" (
    "id" TEXT NOT NULL,
    "sku" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "technicalSpecifications" TEXT,
    "technicalSpecificationsLink" TEXT,
    "unitOfMeasure" TEXT NOT NULL,
    "barcode" TEXT,
    "costPerUnit" DOUBLE PRECISION NOT NULL,
    "estimatedLeadTimeDays" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "models_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "models_sku_key" ON "models"("sku");
