export function setLocalStorage<T>(key: string, value: T) {
  if (typeof window === "undefined") return; // SSR safeguard
  try {
    window.localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.warn(`Error setting localStorage key "${key}":`, error);
  }
}

export function getLocalStorage<T>(key: string, defaultValue?: T): T | undefined {
  if (typeof window === "undefined") return defaultValue; // SSR safeguard
  try {
    const item = window.localStorage.getItem(key);
    return item ? (JSON.parse(item) as T) : defaultValue;
  } catch (error) {
    console.warn(`Error reading localStorage key "${key}":`, error);
    return defaultValue;
  }
}