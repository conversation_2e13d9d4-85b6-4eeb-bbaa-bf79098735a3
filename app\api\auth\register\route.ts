import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import bcrypt from "bcryptjs";
import { sendEmail } from "@/lib/email/send-otp";

export async function POST(req: Request) {
    try {
        const { name, email, password, mobileNumber, gender, dateOfBirth } = await req.json();
        if (!email || !password) {
            return NextResponse.json({ message: "Email and password are required." }, { status: 400 });
        }
        const existingUser = await prisma.user.findUnique({ where: { email } });
        if (existingUser) {
            return NextResponse.json({ message: "User already exists." }, { status: 400 });
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        // Generate 6-digit OTP
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
        await prisma.user.create({
            data: {
                name: name || undefined,
                email,
                password: hashedPassword,
                mobileNumber: mobileNumber || undefined,
                gender: gender || undefined,
                dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
                otp,
                otpExpires,
            },
        });
        console.log("OTP for", email, "is", otp);
        // TODO: Send OTP to user's email
        sendEmail({ to: email, subject: "OTP for login in opins", text: "OTP is: " + otp + ". Please enter it to login." });
        return NextResponse.json({ message: "User registered successfully. Please verify OTP sent to your email." }, { status: 201 });
    } catch (error) {
        console.error(error);
        return NextResponse.json({ message: "Internal server error." }, { status: 500 });
    }
}
