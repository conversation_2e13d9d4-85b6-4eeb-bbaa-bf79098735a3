import * as z from "zod"

export const userAuthSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  email: z.string().email(),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  mobileNumber: z.string().min(8, 'Mobile number must be at least 8 digits').max(20).optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  dateOfBirth: z.string().optional(), // ISO string, will be converted to Date
})
