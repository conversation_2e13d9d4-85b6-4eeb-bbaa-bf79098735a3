{"name": "next-auth-roles-template", "version": "0.0.1", "private": true, "author": {"name": "mickasmt", "url": "https://twitter.com/mickasmt"}, "scripts": {"dev": "next dev", "build": "next build", "turbo": "next dev --turbo", "start": "next start", "lint": "next lint", "preview": "next build && next start", "postinstall": "prisma generate", "email": "email dev --dir emails --port 3333", "remove-content": "node ./setup.mjs"}, "dependencies": {"@auth/prisma-adapter": "^2.4.1", "@hookform/resolvers": "^3.9.0", "@prisma/client": "^5.17.0", "@radix-ui/react-accessible-icon": "^1.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-email/button": "0.0.15", "@react-email/components": "0.0.21", "@react-email/html": "0.0.8", "@t3-oss/env-nextjs": "^0.11.0", "@typescript-eslint/parser": "^7.16.1", "@vercel/analytics": "^1.3.1", "@vercel/og": "^0.6.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^8.2.2", "contentlayer2": "^0.5.0", "date-fns": "^3.6.0", "googleapis": "^161.0.0", "lucide-react": "^0.414.0", "minimist": "^1.2.8", "ms": "^2.1.3", "next": "14.2.5", "next-auth": "5.0.0-beta.18", "next-contentlayer2": "^0.5.0", "next-themes": "^0.3.0", "nodemailer": "^6.9.14", "prop-types": "^15.8.1", "react": "18.3.1", "react-day-picker": "^9.11.0", "react-dom": "18.3.1", "react-email": "2.1.5", "react-hook-form": "^7.52.1", "react-textarea-autosize": "^8.5.3", "recharts": "^2.12.7", "resend": "^3.4.0", "sharp": "^0.33.4", "shiki": "^1.11.0", "sonner": "^1.5.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.13", "@types/node": "^20.14.11", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-tailwindcss": "^3.17.4", "husky": "^9.1.1", "mdast-util-toc": "^7.1.0", "postcss": "^8.4.39", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "pretty-quick": "^4.0.0", "prisma": "^5.17.0", "rehype": "^13.0.1", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.13.2", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "tailwindcss": "^3.4.6", "typescript": "5.5.3", "unist-util-visit": "^5.0.0"}}