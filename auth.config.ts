
import type { NextAuthConfig } from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { prisma } from "@/lib/db";
import bcrypt from "bcryptjs";
import { env } from "@/env.mjs";

import Google from "next-auth/providers/google";


export default {
  providers: [

    Google({
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null;

        // Type assertion to ensure Type<PERSON> knows these are strings
        const email = credentials.email as string;
        const password = credentials.password as string;
        const user = await prisma.user.findUnique({
          where: { email },
          select: {
            id: true,
            name: true,
            email: true,
            password: true,
            emailVerified: true,
            role: true,
            image: true,
          },
        });
        if (!user || typeof user.password !== "string") {
          return null;
        } 
        const isValid = await bcrypt.compare(password, user.password);
        if (!isValid ) {
          return null;
        }
        if (!user.emailVerified) {
          // TODO: Send OTP email
          // sendEmail({ to: email, subject: "OTP for login in opins", text: "OTP is: " + otp + ". Please enter it to login." });
          throw new Error("Please verify your email. OTP has been sent.");
        }
       
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          image: user.image
        };
      }
    })
  ],
} satisfies NextAuthConfig;
